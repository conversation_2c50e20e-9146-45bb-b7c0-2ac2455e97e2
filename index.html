<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Palette Hub - Professional Color Schemes</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="performance.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>Color Palette Hub</h1>
                <p>Professional color schemes for designers & developers</p>
            </div>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search color codes or categories..." class="search-input">
                <button class="search-btn">🔍</button>
            </div>
        </div>
    </header>

    <nav class="nav-sidebar">
        <div class="nav-header">
            <h3>Categories</h3>
            <div style="display: flex; gap: 10px;">
                <button class="clear-filter" id="clearFilter">Clear All</button>
                <a href="stats.html" class="clear-filter" style="text-decoration: none; display: inline-block; text-align: center;">Stats</a>
            </div>
        </div>
        <div class="category-filters">
            <button class="filter-btn active" data-category="all">All Colors</button>
            <button class="filter-btn" data-category="pastel">Pastel</button>
            <button class="filter-btn" data-category="vintage">Vintage</button>
            <button class="filter-btn" data-category="retro">Retro</button>
            <button class="filter-btn" data-category="neon">Neon</button>
            <button class="filter-btn" data-category="gold">Gold</button>
            <button class="filter-btn" data-category="light">Light</button>
            <button class="filter-btn" data-category="dark">Dark</button>
            <button class="filter-btn" data-category="warm">Warm</button>
            <button class="filter-btn" data-category="cold">Cold</button>
            <button class="filter-btn" data-category="summer">Summer</button>
            <button class="filter-btn" data-category="fall">Fall</button>
            <button class="filter-btn" data-category="winter">Winter</button>
            <button class="filter-btn" data-category="spring">Spring</button>
            <button class="filter-btn" data-category="happy">Happy</button>
            <button class="filter-btn" data-category="nature">Nature</button>
            <button class="filter-btn" data-category="earth">Earth</button>
            <button class="filter-btn" data-category="night">Night</button>
            <button class="filter-btn" data-category="space">Space</button>
            <button class="filter-btn" data-category="rainbow">Rainbow</button>
            <button class="filter-btn" data-category="gradient">Gradient</button>
            <button class="filter-btn" data-category="sunset">Sunset</button>
            <button class="filter-btn" data-category="sky">Sky</button>
            <button class="filter-btn" data-category="sea">Sea</button>
            <button class="filter-btn" data-category="kids">Kids</button>
            <button class="filter-btn" data-category="skin">Skin</button>
            <button class="filter-btn" data-category="food">Food</button>
            <button class="filter-btn" data-category="cream">Cream</button>
            <button class="filter-btn" data-category="coffee">Coffee</button>
            <button class="filter-btn" data-category="wedding">Wedding</button>
            <button class="filter-btn" data-category="christmas">Christmas</button>
            <button class="filter-btn" data-category="halloween">Halloween</button>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="results-info">
                <span id="resultsCount">Loading color palettes...</span>
            </div>
            <div class="color-grid" id="colorGrid">
                <!-- Color cards will be dynamically generated here -->
            </div>
        </div>
    </main>

    <div class="toast" id="toast">
        Color code copied to clipboard!
    </div>

    <script src="colors.js"></script>
    <script src="additional-colors.js"></script>
    <script src="mega-colors.js"></script>
    <script src="color-generator.js"></script>
    <script src="script.js"></script>
</body>
</html>
