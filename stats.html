<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Statistics - Color Palette Hub</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .stats-container {
            margin-left: 0;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .category-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .category-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .back-link {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .back-link:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="stats-container">
        <a href="index.html" class="back-link">← Back to Color Palette Hub</a>
        
        <div class="stats-card">
            <h1>Color Palette Statistics</h1>
            <p>Comprehensive overview of our massive color collection</p>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalPalettes">0</div>
                    <div class="stat-label">Total Palettes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalColors">0</div>
                    <div class="stat-label">Individual Colors</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalCategories">31</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="uniqueHex">0</div>
                    <div class="stat-label">Unique Hex Codes</div>
                </div>
            </div>
        </div>
        
        <div class="stats-card">
            <h2>Category Breakdown</h2>
            <div class="category-breakdown" id="categoryStats">
                <!-- Categories will be populated by JavaScript -->
            </div>
        </div>
        
        <div class="stats-card">
            <h2>Color Distribution</h2>
            <div id="colorDistribution">
                <p>Most common color ranges and their frequency in our collection.</p>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="warmColors">0</div>
                        <div class="stat-label">Warm Colors</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="coolColors">0</div>
                        <div class="stat-label">Cool Colors</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="neutralColors">0</div>
                        <div class="stat-label">Neutral Colors</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="brightColors">0</div>
                        <div class="stat-label">Bright Colors</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="stats-card">
            <h2>Achievement Unlocked! 🎉</h2>
            <p style="font-size: 1.2rem; text-align: center; color: #667eea; font-weight: 600;">
                You now have access to over <span id="achievement">5,000</span> individual colors!
            </p>
            <p style="text-align: center; margin-top: 15px;">
                This collection rivals professional color libraries and design tools.
            </p>
        </div>
    </div>

    <script src="colors.js"></script>
    <script src="additional-colors.js"></script>
    <script src="mega-colors.js"></script>
    <script src="color-generator.js"></script>
    
    <script>
        function calculateStats() {
            // Wait for all colors to load
            setTimeout(() => {
                if (typeof colorPalettes === 'undefined' || colorPalettes.length === 0) {
                    setTimeout(calculateStats, 500);
                    return;
                }
                
                const totalPalettes = colorPalettes.length;
                const totalColors = totalPalettes * 4;
                
                // Calculate unique hex codes
                const allColors = new Set();
                colorPalettes.forEach(palette => {
                    palette.colors.forEach(color => {
                        allColors.add(color.toUpperCase());
                    });
                });
                
                // Update main stats
                document.getElementById('totalPalettes').textContent = totalPalettes.toLocaleString();
                document.getElementById('totalColors').textContent = totalColors.toLocaleString();
                document.getElementById('uniqueHex').textContent = allColors.size.toLocaleString();
                document.getElementById('achievement').textContent = totalColors.toLocaleString();
                
                // Calculate category breakdown
                const categories = {};
                colorPalettes.forEach(palette => {
                    if (categories[palette.category]) {
                        categories[palette.category]++;
                    } else {
                        categories[palette.category] = 1;
                    }
                });
                
                // Display category stats
                const categoryContainer = document.getElementById('categoryStats');
                Object.keys(categories).sort().forEach(category => {
                    const div = document.createElement('div');
                    div.className = 'category-item';
                    div.innerHTML = `
                        <strong>${category.charAt(0).toUpperCase() + category.slice(1)}</strong><br>
                        ${categories[category]} palettes (${categories[category] * 4} colors)
                    `;
                    categoryContainer.appendChild(div);
                });
                
                // Calculate color temperature distribution
                let warmCount = 0, coolCount = 0, neutralCount = 0, brightCount = 0;
                
                Array.from(allColors).forEach(color => {
                    const rgb = hexToRgb(color);
                    if (rgb) {
                        const { r, g, b } = rgb;
                        const brightness = (r + g + b) / 3;
                        
                        // Simple color temperature classification
                        if (r > g && r > b) warmCount++;
                        else if (b > r && b > g) coolCount++;
                        else neutralCount++;
                        
                        if (brightness > 180) brightCount++;
                    }
                });
                
                document.getElementById('warmColors').textContent = warmCount.toLocaleString();
                document.getElementById('coolColors').textContent = coolCount.toLocaleString();
                document.getElementById('neutralColors').textContent = neutralCount.toLocaleString();
                document.getElementById('brightColors').textContent = brightCount.toLocaleString();
                
                console.log('Stats calculated:', {
                    totalPalettes,
                    totalColors,
                    uniqueColors: allColors.size,
                    categories: Object.keys(categories).length
                });
                
            }, 2000);
        }
        
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
        
        document.addEventListener('DOMContentLoaded', calculateStats);
    </script>
</body>
</html>