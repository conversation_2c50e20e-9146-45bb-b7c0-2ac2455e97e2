<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convert Colors to JSON</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            color: #2d3748;
            margin: 0 0 10px 0;
        }
        .header p {
            color: #718096;
            margin: 0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        .success {
            background: #48bb78;
        }
        .success:hover {
            background: #38a169;
        }
        .stats {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .stats h3 {
            color: #234e52;
            margin-top: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
        }
        .stat-label {
            color: #718096;
            font-size: 14px;
        }
        .output {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .output pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .progress {
            background: #edf2f7;
            border-radius: 4px;
            height: 8px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: #4299e1;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
        .category-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .category-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Color Palette Converter</h1>
        <p>Convert all JavaScript color files to a single JSON file</p>
    </div>

    <div class="container">
        <div class="button-group">
            <button onclick="convertToJSON()" id="convertBtn">🔄 Convert to JSON</button>
            <button onclick="downloadJSON()" id="downloadBtn" style="display:none;" class="success">📥 Download JSON</button>
            <button onclick="previewJSON()" id="previewBtn" style="display:none;">👁️ Preview JSON</button>
            <button onclick="validateJSON()" id="validateBtn" style="display:none;">✅ Validate JSON</button>
        </div>
        
        <div class="progress" id="progressContainer" style="display:none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status" style="margin: 10px 0; font-weight: 500;"></div>
    </div>
    
    <div id="stats" class="stats" style="display:none;">
        <h3>📊 Conversion Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number" id="totalPalettes">0</div>
                <div class="stat-label">Total Palettes</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalColors">0</div>
                <div class="stat-label">Total Colors</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="fileSize">0 KB</div>
                <div class="stat-label">JSON Size</div>
            </div>
        </div>
        
        <h4>Category Distribution:</h4>
        <div class="category-stats" id="categoryStats"></div>
    </div>
    
    <div id="output" class="output" style="display:none;">
        <h3>JSON Preview (first 2000 characters):</h3>
        <pre id="jsonPreview"></pre>
    </div>

    <!-- Include all color files -->
    <script src="colors.js"></script>
    <script src="additional-colors.js"></script>
    <script src="mega-colors.js"></script>
    <script src="extended-colors.js"></script>
    <script src="premium-colors.js"></script>
    <script src="colorhunt-inspired.js"></script>
    <script src="bonus-colors.js"></script>
    <script src="color-generator.js"></script>

    <script>
        let generatedJSON = null;
        let allPalettes = [];

        function updateProgress(percent, message) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const status = document.getElementById('status');
            
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
            status.textContent = message;
        }

        function getAllColorPalettes() {
            let palettes = [];
            let loadedSources = [];
            
            // Add all color palettes from different files
            if (typeof colorPalettes !== 'undefined') {
                palettes = palettes.concat(colorPalettes);
                loadedSources.push(`colorPalettes (${colorPalettes.length})`);
            }
            
            if (typeof additionalColorPalettes !== 'undefined') {
                palettes = palettes.concat(additionalColorPalettes);
                loadedSources.push(`additionalColorPalettes (${additionalColorPalettes.length})`);
            }
            
            if (typeof megaColorPalettes !== 'undefined') {
                palettes = palettes.concat(megaColorPalettes);
                loadedSources.push(`megaColorPalettes (${megaColorPalettes.length})`);
            }
            
            if (typeof extendedColorPalettes !== 'undefined') {
                palettes = palettes.concat(extendedColorPalettes);
                loadedSources.push(`extendedColorPalettes (${extendedColorPalettes.length})`);
            }
            
            if (typeof premiumColorPalettes !== 'undefined') {
                palettes = palettes.concat(premiumColorPalettes);
                loadedSources.push(`premiumColorPalettes (${premiumColorPalettes.length})`);
            }
            
            if (typeof colorHuntInspiredPalettes !== 'undefined') {
                palettes = palettes.concat(colorHuntInspiredPalettes);
                loadedSources.push(`colorHuntInspiredPalettes (${colorHuntInspiredPalettes.length})`);
            }
            
            if (typeof bonusColorPalettes !== 'undefined') {
                palettes = palettes.concat(bonusColorPalettes);
                loadedSources.push(`bonusColorPalettes (${bonusColorPalettes.length})`);
            }
            
            // Add generated color palettes
            if (typeof generateColorVariations === 'function') {
                try {
                    const generatedPalettes = generateColorVariations();
                    if (generatedPalettes && generatedPalettes.length > 0) {
                        palettes = palettes.concat(generatedPalettes);
                        loadedSources.push(`generated (${generatedPalettes.length})`);
                    }
                } catch (error) {
                    console.warn('Error generating color variations:', error);
                }
            }
            
            console.log('Loaded sources:', loadedSources);
            return palettes;
        }

        async function convertToJSON() {
            try {
                const convertBtn = document.getElementById('convertBtn');
                convertBtn.disabled = true;
                convertBtn.textContent = '🔄 Converting...';
                
                updateProgress(10, 'Loading color palettes...');
                
                // Simulate async processing for better UX
                await new Promise(resolve => setTimeout(resolve, 500));
                
                allPalettes = getAllColorPalettes();
                updateProgress(30, 'Processing palettes...');
                
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // Remove duplicates based on ID
                const uniquePalettes = [];
                const seenIds = new Set();
                
                allPalettes.forEach(palette => {
                    if (!seenIds.has(palette.id)) {
                        seenIds.add(palette.id);
                        uniquePalettes.push(palette);
                    }
                });
                
                updateProgress(60, 'Creating JSON structure...');
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // Create JSON structure
                const jsonData = {
                    metadata: {
                        totalPalettes: uniquePalettes.length,
                        totalColors: uniquePalettes.length * 4,
                        generatedAt: new Date().toISOString(),
                        version: "2.0.0",
                        description: "Color Palette Hub - Professional color schemes for designers & developers",
                        source: "Converted from JavaScript files"
                    },
                    categories: [
                        "pastel", "vintage", "retro", "neon", "gold", "light", "dark", 
                        "warm", "cold", "summer", "fall", "winter", "spring", "happy", 
                        "nature", "earth", "night", "space", "rainbow", "gradient", 
                        "sunset", "sky", "sea", "kids", "skin", "food", "cream", 
                        "coffee", "wedding", "christmas", "halloween"
                    ],
                    palettes: uniquePalettes
                };
                
                updateProgress(80, 'Generating JSON...');
                await new Promise(resolve => setTimeout(resolve, 300));
                
                generatedJSON = JSON.stringify(jsonData, null, 2);
                
                updateProgress(100, 'Conversion completed successfully!');
                
                // Show statistics
                showStatistics(uniquePalettes, generatedJSON);
                
                // Enable buttons
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('previewBtn').style.display = 'inline-block';
                document.getElementById('validateBtn').style.display = 'inline-block';
                
                convertBtn.disabled = false;
                convertBtn.textContent = '✅ Convert to JSON';
                
                setTimeout(() => {
                    convertBtn.textContent = '🔄 Convert to JSON';
                }, 2000);
                
            } catch (error) {
                updateProgress(0, 'Error: ' + error.message);
                console.error('Conversion error:', error);
                
                const convertBtn = document.getElementById('convertBtn');
                convertBtn.disabled = false;
                convertBtn.textContent = '❌ Conversion Failed';
                
                setTimeout(() => {
                    convertBtn.textContent = '🔄 Convert to JSON';
                }, 3000);
            }
        }

        function showStatistics(palettes, jsonString) {
            const categoryStats = {};
            palettes.forEach(palette => {
                categoryStats[palette.category] = (categoryStats[palette.category] || 0) + 1;
            });
            
            // Update main stats
            document.getElementById('totalPalettes').textContent = palettes.length.toLocaleString();
            document.getElementById('totalColors').textContent = (palettes.length * 4).toLocaleString();
            document.getElementById('totalCategories').textContent = Object.keys(categoryStats).length;
            document.getElementById('fileSize').textContent = Math.round(jsonString.length / 1024) + ' KB';
            
            // Update category stats
            const categoryStatsContainer = document.getElementById('categoryStats');
            categoryStatsContainer.innerHTML = '';
            
            Object.entries(categoryStats)
                .sort(([,a], [,b]) => b - a)
                .forEach(([category, count]) => {
                    const item = document.createElement('div');
                    item.className = 'category-item';
                    item.innerHTML = `<strong>${category}</strong><br>${count} palettes`;
                    categoryStatsContainer.appendChild(item);
                });
            
            document.getElementById('stats').style.display = 'block';
        }

        function downloadJSON() {
            if (!generatedJSON) {
                alert('Please convert to JSON first!');
                return;
            }
            
            const blob = new Blob([generatedJSON], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'colors.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            updateProgress(100, 'JSON file downloaded successfully!');
        }

        function previewJSON() {
            if (!generatedJSON) {
                alert('Please convert to JSON first!');
                return;
            }
            
            document.getElementById('jsonPreview').textContent = generatedJSON.substring(0, 2000) + '\n\n... (truncated)';
            document.getElementById('output').style.display = 'block';
        }

        function validateJSON() {
            if (!generatedJSON) {
                alert('Please convert to JSON first!');
                return;
            }
            
            try {
                const parsed = JSON.parse(generatedJSON);
                updateProgress(100, '✅ JSON is valid! Structure verified.');
                
                // Additional validation
                const issues = [];
                if (!parsed.metadata) issues.push('Missing metadata');
                if (!parsed.palettes || !Array.isArray(parsed.palettes)) issues.push('Invalid palettes array');
                if (!parsed.categories || !Array.isArray(parsed.categories)) issues.push('Invalid categories array');
                
                if (issues.length > 0) {
                    updateProgress(50, '⚠️ JSON valid but has issues: ' + issues.join(', '));
                } else {
                    updateProgress(100, '✅ JSON is perfectly valid and well-structured!');
                }
                
            } catch (error) {
                updateProgress(0, '❌ JSON is invalid: ' + error.message);
            }
        }

        // Auto-load on page ready
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress(0, 'Ready to convert. Click "Convert to JSON" to start.');
        });
    </script>
</body>
</html>
