* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.logo p {
    font-size: 0.9rem;
    color: #666;
    font-weight: 400;
}

.search-container {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.search-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.search-input {
    border: none;
    outline: none;
    padding: 12px 20px;
    font-size: 14px;
    background: transparent;
    flex: 1;
    min-width: 300px;
}

.search-btn {
    background: #667eea;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

/* Navigation Sidebar */
.nav-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 120px 20px 20px;
    overflow-y: auto;
    z-index: 90;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.nav-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.clear-filter {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-filter:hover {
    background: #edf2f7;
}

.category-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-btn {
    background: transparent;
    border: none;
    padding: 12px 16px;
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #4a5568;
}

.filter-btn:hover {
    background: #f7fafc;
    color: #2d3748;
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    padding: 30px 0;
    min-height: calc(100vh - 140px);
}

.results-info {
    margin-bottom: 25px;
}

.results-info span {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    display: inline-block;
}

/* Color Grid */
.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
}

/* Color Card */
.color-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.color-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.color-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color1), var(--color2), var(--color3), var(--color4));
}

.card-header {
    margin-bottom: 15px;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
}

.card-category {
    font-size: 0.85rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 15px;
}

.color-item {
    position: relative;
    cursor: pointer;
}

.color-swatch {
    width: 100%;
    height: 80px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.color-swatch:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.color-code {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    text-align: center;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.color-item:hover .color-code {
    opacity: 1;
    transform: translateY(0);
}

.copy-btn {
    background: #48bb78;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    margin-top: 2px;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #38a169;
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: #48bb78;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    font-weight: 500;
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .nav-sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .search-input {
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        text-align: center;
    }
    
    .search-input {
        min-width: 200px;
    }
    
    .color-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .nav-sidebar {
        width: 100%;
    }
    
    .logo h1 {
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .color-palette {
        gap: 8px;
    }
    
    .color-swatch {
        height: 60px;
    }
    
    .search-input {
        min-width: 150px;
        font-size: 14px;
    }
}

/* Custom Scrollbar */
.nav-sidebar::-webkit-scrollbar {
    width: 6px;
}

.nav-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.nav-sidebar::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.nav-sidebar::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
