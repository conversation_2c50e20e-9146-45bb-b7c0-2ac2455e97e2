// <PERSON>ript to generate colors.json from all JavaScript color files
// Run this with Node.js to create the JSON file

const fs = require('fs');
const path = require('path');

// Function to extract color palettes from a JavaScript file
function extractPalettesFromFile(filePath, variableName) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Find the variable declaration
        const regex = new RegExp(`const\\s+${variableName}\\s*=\\s*\\[([\\s\\S]*?)\\];`, 'g');
        const match = regex.exec(content);
        
        if (match) {
            // Extract the array content
            const arrayContent = match[1];
            
            // Use eval to parse the JavaScript array (be careful with this in production)
            const fullCode = `const ${variableName} = [${arrayContent}]; ${variableName}`;
            const palettes = eval(fullCode);
            
            console.log(`Extracted ${palettes.length} palettes from ${filePath}`);
            return palettes;
        } else {
            console.log(`No ${variableName} found in ${filePath}`);
            return [];
        }
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error.message);
        return [];
    }
}

// Main function to generate JSON
function generateColorsJSON() {
    let allPalettes = [];
    
    // Define the files and their variable names
    const colorFiles = [
        { file: 'colors.js', variable: 'colorPalettes' },
        { file: 'additional-colors.js', variable: 'additionalColorPalettes' },
        { file: 'mega-colors.js', variable: 'megaColorPalettes' },
        { file: 'extended-colors.js', variable: 'extendedColorPalettes' },
        { file: 'premium-colors.js', variable: 'premiumColorPalettes' },
        { file: 'colorhunt-inspired.js', variable: 'colorHuntInspiredPalettes' },
        { file: 'bonus-colors.js', variable: 'bonusColorPalettes' }
    ];
    
    // Extract palettes from each file
    colorFiles.forEach(({ file, variable }) => {
        if (fs.existsSync(file)) {
            const palettes = extractPalettesFromFile(file, variable);
            allPalettes = allPalettes.concat(palettes);
        } else {
            console.log(`File ${file} not found, skipping...`);
        }
    });
    
    // Generate color variations if the function exists
    try {
        // Read and execute color-generator.js
        if (fs.existsSync('color-generator.js')) {
            const generatorContent = fs.readFileSync('color-generator.js', 'utf8');
            eval(generatorContent);
            
            if (typeof generateColorVariations === 'function') {
                const generatedPalettes = generateColorVariations();
                if (generatedPalettes && generatedPalettes.length > 0) {
                    allPalettes = allPalettes.concat(generatedPalettes);
                    console.log(`Added ${generatedPalettes.length} generated palettes`);
                }
            }
        }
    } catch (error) {
        console.warn('Error generating color variations:', error.message);
    }
    
    // Remove duplicates based on ID
    const uniquePalettes = [];
    const seenIds = new Set();
    
    allPalettes.forEach(palette => {
        if (!seenIds.has(palette.id)) {
            seenIds.add(palette.id);
            uniquePalettes.push(palette);
        }
    });
    
    console.log(`Total unique palettes: ${uniquePalettes.length}`);
    console.log(`Total colors: ${uniquePalettes.length * 4}`);
    
    // Create the JSON structure
    const jsonData = {
        metadata: {
            totalPalettes: uniquePalettes.length,
            totalColors: uniquePalettes.length * 4,
            generatedAt: new Date().toISOString(),
            version: "2.0.0"
        },
        categories: [
            "pastel", "vintage", "retro", "neon", "gold", "light", "dark", 
            "warm", "cold", "summer", "fall", "winter", "spring", "happy", 
            "nature", "earth", "night", "space", "rainbow", "gradient", 
            "sunset", "sky", "sea", "kids", "skin", "food", "cream", 
            "coffee", "wedding", "christmas", "halloween"
        ],
        palettes: uniquePalettes
    };
    
    // Write to JSON file
    fs.writeFileSync('colors.json', JSON.stringify(jsonData, null, 2));
    console.log('colors.json generated successfully!');
    
    // Generate statistics
    const categoryStats = {};
    uniquePalettes.forEach(palette => {
        categoryStats[palette.category] = (categoryStats[palette.category] || 0) + 1;
    });
    
    console.log('\nCategory Statistics:');
    Object.entries(categoryStats).forEach(([category, count]) => {
        console.log(`${category}: ${count} palettes`);
    });
}

// Run the generator
if (require.main === module) {
    generateColorsJSON();
}

module.exports = { generateColorsJSON };
