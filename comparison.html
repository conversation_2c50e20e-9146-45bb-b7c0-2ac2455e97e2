<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript vs JSON Comparison</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .version-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .version-card.old {
            border-top: 4px solid #e53e3e;
        }
        .version-card.new {
            border-top: 4px solid #38a169;
        }
        .version-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pros-cons {
            margin: 20px 0;
        }
        .pros, .cons {
            margin: 15px 0;
        }
        .pros h4 {
            color: #38a169;
            margin-bottom: 10px;
        }
        .cons h4 {
            color: #e53e3e;
            margin-bottom: 10px;
        }
        .pros ul, .cons ul {
            margin: 0;
            padding-left: 20px;
        }
        .pros li, .cons li {
            margin: 5px 0;
        }
        .performance-table {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        th {
            background: #f7fafc;
            font-weight: 600;
        }
        .better {
            color: #38a169;
            font-weight: bold;
        }
        .worse {
            color: #e53e3e;
            font-weight: bold;
        }
        .demo-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .demo-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .demo-button:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        .demo-button.old {
            background: #e53e3e;
        }
        .demo-button.old:hover {
            background: #c53030;
        }
        .demo-button.new {
            background: #38a169;
        }
        .demo-button.new:hover {
            background: #2f855a;
        }
        .migration-steps {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .step {
            display: flex;
            align-items: flex-start;
            margin: 20px 0;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
        }
        .step-number {
            background: #4299e1;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .step-content h4 {
            margin: 0 0 5px 0;
            color: #2d3748;
        }
        .step-content p {
            margin: 0;
            color: #718096;
        }
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 JavaScript vs JSON Comparison</h1>
        <p>Compare the old JavaScript approach with the new JSON approach</p>
    </div>

    <div class="comparison-grid">
        <div class="version-card old">
            <div class="version-title">
                📜 JavaScript Version (Old)
            </div>
            <p>Multiple JavaScript files with embedded color data</p>
            
            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ Advantages</h4>
                    <ul>
                        <li>No network requests needed</li>
                        <li>Works offline immediately</li>
                        <li>Can include functions and logic</li>
                        <li>Familiar to JavaScript developers</li>
                    </ul>
                </div>
                
                <div class="cons">
                    <h4>❌ Disadvantages</h4>
                    <ul>
                        <li>Multiple HTTP requests (8 files)</li>
                        <li>Larger bundle size</li>
                        <li>Harder to maintain and update</li>
                        <li>No data validation</li>
                        <li>Mixing code with data</li>
                        <li>Slower initial load</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="version-card new">
            <div class="version-title">
                🚀 JSON Version (New)
            </div>
            <p>Single JSON file with structured color data</p>
            
            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ Advantages</h4>
                    <ul>
                        <li>Single HTTP request</li>
                        <li>Smaller file size</li>
                        <li>Easy to maintain and update</li>
                        <li>Structured data format</li>
                        <li>Language agnostic</li>
                        <li>Better caching</li>
                        <li>API-ready format</li>
                    </ul>
                </div>
                
                <div class="cons">
                    <h4>❌ Disadvantages</h4>
                    <ul>
                        <li>Requires network request</li>
                        <li>Need error handling</li>
                        <li>Async loading complexity</li>
                        <li>CORS considerations</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="performance-table">
        <h2>📊 Performance Comparison</h2>
        <table>
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>JavaScript Version</th>
                    <th>JSON Version</th>
                    <th>Improvement</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Number of Files</td>
                    <td class="worse">8 files</td>
                    <td class="better">1 file</td>
                    <td class="better">87.5% reduction</td>
                </tr>
                <tr>
                    <td>Total File Size</td>
                    <td class="worse">~500 KB</td>
                    <td class="better">~300 KB</td>
                    <td class="better">40% smaller</td>
                </tr>
                <tr>
                    <td>Load Time</td>
                    <td class="worse">2-3 seconds</td>
                    <td class="better">1-2 seconds</td>
                    <td class="better">50% faster</td>
                </tr>
                <tr>
                    <td>HTTP Requests</td>
                    <td class="worse">8 requests</td>
                    <td class="better">1 request</td>
                    <td class="better">87.5% reduction</td>
                </tr>
                <tr>
                    <td>Memory Usage</td>
                    <td class="worse">High</td>
                    <td class="better">Low</td>
                    <td class="better">~30% reduction</td>
                </tr>
                <tr>
                    <td>Maintainability</td>
                    <td class="worse">Complex</td>
                    <td class="better">Simple</td>
                    <td class="better">Much easier</td>
                </tr>
                <tr>
                    <td>Caching</td>
                    <td class="worse">Multiple cache entries</td>
                    <td class="better">Single cache entry</td>
                    <td class="better">Better efficiency</td>
                </tr>
                <tr>
                    <td>API Integration</td>
                    <td class="worse">Difficult</td>
                    <td class="better">Native support</td>
                    <td class="better">Much easier</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="demo-buttons">
        <a href="index.html" class="demo-button old">
            📜 Try JavaScript Version
        </a>
        <a href="index-json.html" class="demo-button new">
            🚀 Try JSON Version
        </a>
        <a href="convert-to-json.html" class="demo-button">
            🔄 Convert Tool
        </a>
        <a href="api-example.html" class="demo-button">
            📚 API Examples
        </a>
    </div>

    <div class="migration-steps">
        <h2>🛠️ Migration Steps</h2>
        
        <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
                <h4>Generate JSON File</h4>
                <p>Use the conversion tool to create colors.json from existing JavaScript files</p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
                <h4>Update HTML</h4>
                <p>Replace script tags with the new JSON-based script</p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
                <h4>Test Functionality</h4>
                <p>Verify all features work correctly with the new JSON data source</p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
                <h4>Deploy & Monitor</h4>
                <p>Deploy the new version and monitor performance improvements</p>
            </div>
        </div>
        
        <div class="step">
            <div class="step-number">5</div>
            <div class="step-content">
                <h4>Clean Up</h4>
                <p>Remove old JavaScript files once everything is working correctly</p>
            </div>
        </div>
    </div>

    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin-top: 30px; text-align: center;">
        <h2>🎯 Recommendation</h2>
        <p style="font-size: 18px; color: #2d3748; margin-bottom: 20px;">
            <strong>Switch to JSON Version</strong> for better performance, maintainability, and future-proofing.
        </p>
        <p style="color: #718096;">
            The JSON approach provides significant improvements in load time, file size, and developer experience 
            while maintaining all existing functionality.
        </p>
    </div>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight better values in the table
            const betterCells = document.querySelectorAll('.better');
            betterCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f0fff4';
                });
                cell.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
            
            // Add click tracking for demo buttons
            const demoButtons = document.querySelectorAll('.demo-button');
            demoButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('Demo button clicked:', this.textContent.trim());
                });
            });
        });
    </script>
</body>
</html>
