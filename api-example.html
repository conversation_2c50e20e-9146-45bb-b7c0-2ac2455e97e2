<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color API Example</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .api-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .demo-area {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .color-sample {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            margin: 5px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .palette-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .palette-colors {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3182ce;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e6fffa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Color Palette API Examples</h1>
        <p>How to use colors.json in different applications</p>
    </div>

    <div class="api-section">
        <h2>📊 API Information</h2>
        <div id="apiInfo" class="demo-area">
            <p>Loading API information...</p>
        </div>
        <div class="stats" id="statsGrid"></div>
    </div>

    <div class="api-section">
        <h2>🔍 Basic Usage Examples</h2>
        
        <h3>1. Load All Colors</h3>
        <div class="code-block">
// Fetch all color palettes
async function loadColors() {
    try {
        const response = await fetch('colors.json');
        const data = await response.json();
        
        console.log(`Loaded ${data.metadata.totalPalettes} palettes`);
        console.log(`Total colors: ${data.metadata.totalColors}`);
        
        return data.palettes;
    } catch (error) {
        console.error('Error loading colors:', error);
    }
}

// Usage
const palettes = await loadColors();
        </div>
        
        <button onclick="demonstrateLoadColors()">🔄 Try This Example</button>
        <div id="loadColorsResult" class="demo-area" style="display:none;"></div>

        <h3>2. Filter by Category</h3>
        <div class="code-block">
// Filter palettes by category
function filterByCategory(palettes, category) {
    return palettes.filter(palette => 
        palette.category.toLowerCase() === category.toLowerCase()
    );
}

// Usage
const pastelColors = filterByCategory(palettes, 'pastel');
const vintageColors = filterByCategory(palettes, 'vintage');
        </div>
        
        <button onclick="demonstrateFilter()">🎨 Try Filter Example</button>
        <div id="filterResult" class="demo-area" style="display:none;"></div>

        <h3>3. Search Colors</h3>
        <div class="code-block">
// Search in palette names and color codes
function searchColors(palettes, searchTerm) {
    const term = searchTerm.toLowerCase();
    return palettes.filter(palette => {
        const nameMatch = palette.name.toLowerCase().includes(term);
        const colorMatch = palette.colors.some(color => 
            color.toLowerCase().includes(term)
        );
        return nameMatch || colorMatch;
    });
}

// Usage
const redColors = searchColors(palettes, 'red');
const blueHexColors = searchColors(palettes, '#0000ff');
        </div>
        
        <button onclick="demonstrateSearch()">🔍 Try Search Example</button>
        <div id="searchResult" class="demo-area" style="display:none;"></div>

        <h3>4. Random Color Palette</h3>
        <div class="code-block">
// Get random palette
function getRandomPalette(palettes) {
    const randomIndex = Math.floor(Math.random() * palettes.length);
    return palettes[randomIndex];
}

// Get random palette from specific category
function getRandomPaletteFromCategory(palettes, category) {
    const filtered = filterByCategory(palettes, category);
    return getRandomPalette(filtered);
}

// Usage
const randomPalette = getRandomPalette(palettes);
const randomPastel = getRandomPaletteFromCategory(palettes, 'pastel');
        </div>
        
        <button onclick="demonstrateRandom()">🎲 Try Random Example</button>
        <div id="randomResult" class="demo-area" style="display:none;"></div>
    </div>

    <div class="api-section">
        <h2>🛠️ Advanced Usage</h2>
        
        <h3>5. Color Analysis</h3>
        <div class="code-block">
// Analyze color distribution
function analyzeColors(palettes) {
    const categoryStats = {};
    const colorFrequency = {};
    
    palettes.forEach(palette => {
        // Count categories
        categoryStats[palette.category] = 
            (categoryStats[palette.category] || 0) + 1;
        
        // Count color usage
        palette.colors.forEach(color => {
            colorFrequency[color] = (colorFrequency[color] || 0) + 1;
        });
    });
    
    return { categoryStats, colorFrequency };
}
        </div>
        
        <button onclick="demonstrateAnalysis()">📈 Try Analysis Example</button>
        <div id="analysisResult" class="demo-area" style="display:none;"></div>

        <h3>6. Export Specific Categories</h3>
        <div class="code-block">
// Export palettes for specific use case
function exportForDesign(palettes, categories) {
    const filtered = palettes.filter(palette => 
        categories.includes(palette.category)
    );
    
    return {
        metadata: {
            exportedAt: new Date().toISOString(),
            totalPalettes: filtered.length,
            categories: categories
        },
        palettes: filtered
    };
}

// Usage - Export wedding and pastel colors
const designColors = exportForDesign(palettes, ['wedding', 'pastel', 'light']);
        </div>
        
        <button onclick="demonstrateExport()">📤 Try Export Example</button>
        <div id="exportResult" class="demo-area" style="display:none;"></div>
    </div>

    <div class="api-section">
        <h2>🎯 Integration Examples</h2>
        
        <h3>React Component Example</h3>
        <div class="code-block">
import React, { useState, useEffect } from 'react';

function ColorPalette() {
    const [palettes, setPalettes] = useState([]);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        fetch('/colors.json')
            .then(response => response.json())
            .then(data => {
                setPalettes(data.palettes);
                setLoading(false);
            })
            .catch(error => {
                console.error('Error:', error);
                setLoading(false);
            });
    }, []);
    
    if (loading) return &lt;div&gt;Loading colors...&lt;/div&gt;;
    
    return (
        &lt;div&gt;
            {palettes.map(palette => (
                &lt;div key={palette.id}&gt;
                    &lt;h3&gt;{palette.name}&lt;/h3&gt;
                    &lt;div style={{display: 'flex'}}&gt;
                        {palette.colors.map(color => (
                            &lt;div 
                                key={color}
                                style={{
                                    width: 50,
                                    height: 50,
                                    backgroundColor: color
                                }}
                            /&gt;
                        ))}
                    &lt;/div&gt;
                &lt;/div&gt;
            ))}
        &lt;/div&gt;
    );
}
        </div>

        <h3>Vue.js Component Example</h3>
        <div class="code-block">
&lt;template&gt;
  &lt;div&gt;
    &lt;div v-if="loading"&gt;Loading colors...&lt;/div&gt;
    &lt;div v-else&gt;
      &lt;div v-for="palette in palettes" :key="palette.id"&gt;
        &lt;h3&gt;{{ palette.name }}&lt;/h3&gt;
        &lt;div class="color-row"&gt;
          &lt;div 
            v-for="color in palette.colors" 
            :key="color"
            :style="{ backgroundColor: color }"
            class="color-swatch"
          &gt;&lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
export default {
  data() {
    return {
      palettes: [],
      loading: true
    }
  },
  async mounted() {
    try {
      const response = await fetch('/colors.json');
      const data = await response.json();
      this.palettes = data.palettes;
    } catch (error) {
      console.error('Error:', error);
    } finally {
      this.loading = false;
    }
  }
}
&lt;/script&gt;
        </div>
    </div>

    <script>
        let colorData = null;

        // Load color data on page load
        async function loadColorData() {
            try {
                const response = await fetch('colors.json');
                colorData = await response.json();
                
                // Update API info
                document.getElementById('apiInfo').innerHTML = `
                    <h4>📋 API Metadata</h4>
                    <p><strong>Total Palettes:</strong> ${colorData.metadata.totalPalettes}</p>
                    <p><strong>Total Colors:</strong> ${colorData.metadata.totalColors}</p>
                    <p><strong>Version:</strong> ${colorData.metadata.version}</p>
                    <p><strong>Generated:</strong> ${new Date(colorData.metadata.generatedAt).toLocaleString()}</p>
                `;
                
                // Update stats
                const categoryStats = {};
                colorData.palettes.forEach(palette => {
                    categoryStats[palette.category] = (categoryStats[palette.category] || 0) + 1;
                });
                
                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = Object.entries(categoryStats)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 6)
                    .map(([category, count]) => `
                        <div class="stat-card">
                            <div class="stat-number">${count}</div>
                            <div>${category}</div>
                        </div>
                    `).join('');
                
            } catch (error) {
                document.getElementById('apiInfo').innerHTML = `
                    <p style="color: red;">Error loading colors.json: ${error.message}</p>
                    <p>Make sure the file exists and the server is running.</p>
                `;
            }
        }

        function demonstrateLoadColors() {
            if (!colorData) {
                alert('Color data not loaded yet!');
                return;
            }
            
            const result = document.getElementById('loadColorsResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>✅ Successfully loaded colors!</h4>
                <p>Total palettes: ${colorData.palettes.length}</p>
                <p>First palette: ${colorData.palettes[0].name}</p>
                <div class="palette-colors">
                    ${colorData.palettes[0].colors.map(color => 
                        `<div class="color-sample" style="background-color: ${color}" title="${color}"></div>`
                    ).join('')}
                </div>
            `;
        }

        function demonstrateFilter() {
            if (!colorData) return;
            
            const pastelPalettes = colorData.palettes.filter(p => p.category === 'pastel');
            const result = document.getElementById('filterResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>🎨 Pastel Colors Found: ${pastelPalettes.length}</h4>
                ${pastelPalettes.slice(0, 3).map(palette => `
                    <div class="palette-card">
                        <strong>${palette.name}</strong>
                        <div class="palette-colors">
                            ${palette.colors.map(color => 
                                `<div class="color-sample" style="background-color: ${color}" title="${color}"></div>`
                            ).join('')}
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function demonstrateSearch() {
            if (!colorData) return;
            
            const searchResults = colorData.palettes.filter(palette => 
                palette.name.toLowerCase().includes('blue') || 
                palette.colors.some(color => color.toLowerCase().includes('blue'))
            );
            
            const result = document.getElementById('searchResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>🔍 Search Results for "blue": ${searchResults.length}</h4>
                ${searchResults.slice(0, 2).map(palette => `
                    <div class="palette-card">
                        <strong>${palette.name}</strong> (${palette.category})
                        <div class="palette-colors">
                            ${palette.colors.map(color => 
                                `<div class="color-sample" style="background-color: ${color}" title="${color}"></div>`
                            ).join('')}
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function demonstrateRandom() {
            if (!colorData) return;
            
            const randomPalette = colorData.palettes[Math.floor(Math.random() * colorData.palettes.length)];
            const result = document.getElementById('randomResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>🎲 Random Palette</h4>
                <div class="palette-card">
                    <strong>${randomPalette.name}</strong> (${randomPalette.category})
                    <div class="palette-colors">
                        ${randomPalette.colors.map(color => 
                            `<div class="color-sample" style="background-color: ${color}" title="${color}"></div>`
                        ).join('')}
                    </div>
                </div>
            `;
        }

        function demonstrateAnalysis() {
            if (!colorData) return;
            
            const categoryStats = {};
            colorData.palettes.forEach(palette => {
                categoryStats[palette.category] = (categoryStats[palette.category] || 0) + 1;
            });
            
            const topCategories = Object.entries(categoryStats)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5);
            
            const result = document.getElementById('analysisResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>📈 Color Analysis</h4>
                <p><strong>Top 5 Categories:</strong></p>
                <ul>
                    ${topCategories.map(([category, count]) => 
                        `<li>${category}: ${count} palettes</li>`
                    ).join('')}
                </ul>
            `;
        }

        function demonstrateExport() {
            if (!colorData) return;
            
            const designCategories = ['wedding', 'pastel', 'light'];
            const exportData = {
                metadata: {
                    exportedAt: new Date().toISOString(),
                    categories: designCategories
                },
                palettes: colorData.palettes.filter(p => designCategories.includes(p.category))
            };
            
            const result = document.getElementById('exportResult');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>📤 Export Results</h4>
                <p>Exported ${exportData.palettes.length} palettes for design use</p>
                <p>Categories: ${designCategories.join(', ')}</p>
                <button onclick="downloadExport(${JSON.stringify(exportData).replace(/"/g, '&quot;')})">
                    💾 Download Export
                </button>
            `;
        }

        function downloadExport(data) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'design-colors-export.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadColorData);
    </script>
</body>
</html>
