// Global variables
let filteredPalettes = [];
let currentFilter = 'all';
let allColorPalettes = [];
let isLoading = true;

// Load color data from JSON file
async function loadColorData() {
    try {
        console.log('Loading color data from JSON...');
        const response = await fetch('colors.json');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Store the palettes globally
        allColorPalettes = data.palettes || [];
        
        console.log(`Loaded ${allColorPalettes.length} palettes from JSON`);
        console.log(`Total colors: ${data.metadata?.totalColors || allColorPalettes.length * 4}`);
        
        // Initialize the application
        initializeFilteredPalettes();
        
        return data;
        
    } catch (error) {
        console.error('Error loading color data:', error);
        
        // Fallback to JavaScript files if JSON fails
        console.log('Falling back to JavaScript color files...');
        loadFromJavaScriptFiles();
    }
}

// Fallback function to load from JavaScript files
function loadFromJavaScriptFiles() {
    // This function combines all color palettes from JavaScript files
    // (keeping the original functionality as backup)
    let allPalettes = [];
    
    // Add main color palettes
    if (typeof colorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(colorPalettes);
    }
    
    // Add additional color palettes
    if (typeof additionalColorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(additionalColorPalettes);
    }
    
    // Add mega color palettes
    if (typeof megaColorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(megaColorPalettes);
    }
    
    // Add extended color palettes
    if (typeof extendedColorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(extendedColorPalettes);
    }
    
    // Add premium color palettes
    if (typeof premiumColorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(premiumColorPalettes);
    }
    
    // Add ColorHunt inspired palettes
    if (typeof colorHuntInspiredPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(colorHuntInspiredPalettes);
    }
    
    // Add bonus color palettes
    if (typeof bonusColorPalettes !== 'undefined') {
        allPalettes = allPalettes.concat(bonusColorPalettes);
    }
    
    // Add generated color palettes
    if (typeof generateColorVariations === 'function') {
        try {
            const generatedPalettes = generateColorVariations();
            if (generatedPalettes && generatedPalettes.length > 0) {
                allPalettes = allPalettes.concat(generatedPalettes);
            }
        } catch (error) {
            console.warn('Error generating color variations:', error);
        }
    }
    
    allColorPalettes = allPalettes;
    console.log(`Loaded ${allColorPalettes.length} palettes from JavaScript files`);
    
    initializeFilteredPalettes();
}

// Initialize filtered palettes after colors are loaded
function initializeFilteredPalettes() {
    if (allColorPalettes.length > 0) {
        filteredPalettes = [...allColorPalettes];
        isLoading = false;
        renderColorPalettes();
        updateResultsCount();
        console.log(`Initialized with ${allColorPalettes.length} palettes (${allColorPalettes.length * 4} individual colors)`);
    } else {
        // Show loading message
        const colorGrid = document.getElementById('colorGrid');
        if (colorGrid) {
            colorGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">Loading color palettes...</div>';
        }
        
        // Retry after a short delay
        setTimeout(initializeFilteredPalettes, 100);
    }
}

// DOM elements
const colorGrid = document.getElementById('colorGrid');
const searchInput = document.getElementById('searchInput');
const resultsCount = document.getElementById('resultsCount');
const filterButtons = document.querySelectorAll('.filter-btn');
const clearFilterBtn = document.getElementById('clearFilter');
const toast = document.getElementById('toast');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Load color data from JSON
    loadColorData();
    
    // Setup event listeners
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // Filter buttons
    filterButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const category = e.target.dataset.category;
            setActiveFilter(category);
            filterPalettes(category);
        });
    });
    
    // Clear filter button
    if (clearFilterBtn) {
        clearFilterBtn.addEventListener('click', () => {
            setActiveFilter('all');
            filterPalettes('all');
            if (searchInput) {
                searchInput.value = '';
            }
        });
    }
}

// Render color palettes
function renderColorPalettes() {
    if (!colorGrid || isLoading) return;
    
    if (filteredPalettes.length === 0) {
        colorGrid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                <h3>No color palettes found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        return;
    }
    
    const paletteCards = filteredPalettes.map(palette => {
        const colorItems = palette.colors.map((color, index) => `
            <div class="color-item" onclick="copyToClipboard('${color}')">
                <div class="color-swatch" style="background-color: ${color};">
                    <div class="color-code">${color}</div>
                </div>
            </div>
        `).join('');
        
        return `
            <div class="color-card" style="--color1: ${palette.colors[0]}; --color2: ${palette.colors[1]}; --color3: ${palette.colors[2]}; --color4: ${palette.colors[3]};">
                <div class="card-header">
                    <h3 class="card-title">${palette.name}</h3>
                    <span class="card-category">${palette.category}</span>
                </div>
                <div class="color-palette">
                    ${colorItems}
                </div>
            </div>
        `;
    }).join('');
    
    colorGrid.innerHTML = paletteCards;
}

// Filter palettes by category
function filterPalettes(category) {
    currentFilter = category;
    
    if (category === 'all') {
        filteredPalettes = [...allColorPalettes];
    } else {
        filteredPalettes = allColorPalettes.filter(palette => 
            palette.category.toLowerCase() === category.toLowerCase()
        );
    }
    
    // Apply search filter if there's a search term
    const searchTerm = searchInput ? searchInput.value.trim() : '';
    if (searchTerm) {
        applySearchFilter(searchTerm);
    } else {
        renderColorPalettes();
        updateResultsCount();
    }
}

// Handle search functionality
function handleSearch(e) {
    const searchTerm = e.target.value.trim().toLowerCase();
    applySearchFilter(searchTerm);
}

// Apply search filter
function applySearchFilter(searchTerm) {
    if (!searchTerm) {
        // If no search term, show all palettes for current category
        filterPalettes(currentFilter);
        return;
    }
    
    // Start with category filtered palettes
    let basePalettes = currentFilter === 'all' ? allColorPalettes : 
        allColorPalettes.filter(palette => palette.category.toLowerCase() === currentFilter.toLowerCase());
    
    // Apply search filter
    filteredPalettes = basePalettes.filter(palette => {
        // Search in name, category, and color codes
        const nameMatch = palette.name.toLowerCase().includes(searchTerm);
        const categoryMatch = palette.category.toLowerCase().includes(searchTerm);
        const colorMatch = palette.colors.some(color => 
            color.toLowerCase().includes(searchTerm)
        );
        
        return nameMatch || categoryMatch || colorMatch;
    });
    
    renderColorPalettes();
    updateResultsCount();
}

// Set active filter button
function setActiveFilter(category) {
    filterButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.category === category) {
            btn.classList.add('active');
        }
    });
}

// Update results count
function updateResultsCount() {
    if (!resultsCount) return;
    
    const totalColors = filteredPalettes.length * 4;
    resultsCount.textContent = `Showing ${filteredPalettes.length} palettes (${totalColors} colors)`;
}

// Copy color to clipboard
function copyToClipboard(color) {
    navigator.clipboard.writeText(color).then(() => {
        showToast(`Color ${color} copied to clipboard!`);
    }).catch(err => {
        console.error('Failed to copy color: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = color;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(`Color ${color} copied to clipboard!`);
    });
}

// Show toast notification
function showToast(message) {
    if (!toast) return;
    
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}
