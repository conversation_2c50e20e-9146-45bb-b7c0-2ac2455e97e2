/* Performance optimizations for large number of color palettes */

/* Virtual scrolling and lazy loading improvements */
.color-grid {
    contain: layout style paint;
    will-change: transform;
}

.color-card {
    contain: layout style paint;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Smooth scrolling for better performance */
html {
    scroll-behavior: smooth;
}

/* Optimize hover effects for better performance */
.color-swatch:hover {
    transform: scale(1.05) translateZ(0);
    will-change: transform;
}

.color-card:hover {
    transform: translateY(-5px) translateZ(0);
    will-change: transform;
}

/* Loading animation for large datasets */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Optimize text rendering */
.card-title,
.card-category,
.color-code {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Results counter styling for large numbers */
.results-info span {
    font-variant-numeric: tabular-nums;
    font-weight: 600;
}

/* Improved responsiveness for large grids */
@media (max-width: 1200px) {
    .color-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 900px) {
    .color-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 600px) {
    .color-grid {
        grid-template-columns: 1fr;
    }
}

/* Pagination controls for better performance with large datasets */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 30px 0;
    gap: 10px;
}

.pagination button {
    padding: 10px 15px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover {
    background: #667eea;
    color: white;
}

.pagination button.active {
    background: #667eea;
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Color count badge */
.color-count-badge {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    z-index: 100;
}

/* Performance indicator */
.performance-stats {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 12px;
    font-family: monospace;
    z-index: 100;
    opacity: 0.7;
}

/* Infinite scroll indicator */
.load-more {
    text-align: center;
    padding: 20px;
    margin: 20px 0;
}

.load-more button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Search improvements for large datasets */
.search-results-highlight {
    background: rgba(255, 255, 0, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

/* Category filter improvements */
.category-filters {
    max-height: 400px;
    overflow-y: auto;
}

.category-filters::-webkit-scrollbar {
    width: 6px;
}

.category-filters::-webkit-scrollbar-track {
    background: transparent;
}

.category-filters::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .color-card,
    .color-swatch,
    .color-card:hover,
    .color-swatch:hover {
        transition: none;
        transform: none;
        animation: none;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Print styles for color palettes */
@media print {
    .header,
    .nav-sidebar,
    .toast,
    .mobile-toggle,
    .color-count-badge,
    .performance-stats {
        display: none;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .color-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .color-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}