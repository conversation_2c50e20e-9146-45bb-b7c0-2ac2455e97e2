<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Colors JSON</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .stats {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Generate Colors JSON File</h1>
    <div class="container">
        <p>This tool will combine all color palettes from JavaScript files into a single JSON file.</p>
        <button onclick="generateJSON()">Generate colors.json</button>
        <button onclick="downloadJSON()" id="downloadBtn" style="display:none; margin-left: 10px;">Download JSON</button>
    </div>
    
    <div id="stats" class="stats" style="display:none;">
        <h3>Statistics:</h3>
        <div id="statsContent"></div>
    </div>
    
    <div id="output" class="output" style="display:none;">
        <h3>Generated JSON (first 1000 characters):</h3>
        <pre id="jsonPreview"></pre>
    </div>

    <!-- Include all color files -->
    <script src="colors.js"></script>
    <script src="additional-colors.js"></script>
    <script src="mega-colors.js"></script>
    <script src="extended-colors.js"></script>
    <script src="premium-colors.js"></script>
    <script src="colorhunt-inspired.js"></script>
    <script src="bonus-colors.js"></script>
    <script src="color-generator.js"></script>

    <script>
        let generatedJSON = null;

        function getAllColorPalettes() {
            let allPalettes = [];
            
            // Add all color palettes from different files
            if (typeof colorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(colorPalettes);
                console.log('Added colorPalettes:', colorPalettes.length);
            }
            
            if (typeof additionalColorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(additionalColorPalettes);
                console.log('Added additionalColorPalettes:', additionalColorPalettes.length);
            }
            
            if (typeof megaColorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(megaColorPalettes);
                console.log('Added megaColorPalettes:', megaColorPalettes.length);
            }
            
            if (typeof extendedColorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(extendedColorPalettes);
                console.log('Added extendedColorPalettes:', extendedColorPalettes.length);
            }
            
            if (typeof premiumColorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(premiumColorPalettes);
                console.log('Added premiumColorPalettes:', premiumColorPalettes.length);
            }
            
            if (typeof colorHuntInspiredPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(colorHuntInspiredPalettes);
                console.log('Added colorHuntInspiredPalettes:', colorHuntInspiredPalettes.length);
            }
            
            if (typeof bonusColorPalettes !== 'undefined') {
                allPalettes = allPalettes.concat(bonusColorPalettes);
                console.log('Added bonusColorPalettes:', bonusColorPalettes.length);
            }
            
            // Add generated color palettes
            if (typeof generateColorVariations === 'function') {
                try {
                    const generatedPalettes = generateColorVariations();
                    if (generatedPalettes && generatedPalettes.length > 0) {
                        allPalettes = allPalettes.concat(generatedPalettes);
                        console.log('Added generated palettes:', generatedPalettes.length);
                    }
                } catch (error) {
                    console.warn('Error generating color variations:', error);
                }
            }
            
            return allPalettes;
        }

        function generateJSON() {
            try {
                const allPalettes = getAllColorPalettes();
                
                // Remove duplicates based on ID
                const uniquePalettes = [];
                const seenIds = new Set();
                
                allPalettes.forEach(palette => {
                    if (!seenIds.has(palette.id)) {
                        seenIds.add(palette.id);
                        uniquePalettes.push(palette);
                    }
                });
                
                // Create JSON structure
                const jsonData = {
                    metadata: {
                        totalPalettes: uniquePalettes.length,
                        totalColors: uniquePalettes.length * 4,
                        generatedAt: new Date().toISOString(),
                        version: "2.0.0",
                        description: "Color Palette Hub - Professional color schemes for designers & developers"
                    },
                    categories: [
                        "pastel", "vintage", "retro", "neon", "gold", "light", "dark", 
                        "warm", "cold", "summer", "fall", "winter", "spring", "happy", 
                        "nature", "earth", "night", "space", "rainbow", "gradient", 
                        "sunset", "sky", "sea", "kids", "skin", "food", "cream", 
                        "coffee", "wedding", "christmas", "halloween"
                    ],
                    palettes: uniquePalettes
                };
                
                generatedJSON = JSON.stringify(jsonData, null, 2);
                
                // Show statistics
                const categoryStats = {};
                uniquePalettes.forEach(palette => {
                    categoryStats[palette.category] = (categoryStats[palette.category] || 0) + 1;
                });
                
                let statsHTML = `
                    <p><strong>Total Palettes:</strong> ${uniquePalettes.length}</p>
                    <p><strong>Total Colors:</strong> ${uniquePalettes.length * 4}</p>
                    <p><strong>Generated At:</strong> ${new Date().toLocaleString()}</p>
                    <h4>Category Distribution:</h4>
                    <ul>
                `;
                
                Object.entries(categoryStats).sort().forEach(([category, count]) => {
                    statsHTML += `<li>${category}: ${count} palettes</li>`;
                });
                
                statsHTML += '</ul>';
                
                document.getElementById('statsContent').innerHTML = statsHTML;
                document.getElementById('stats').style.display = 'block';
                
                // Show preview
                document.getElementById('jsonPreview').textContent = generatedJSON.substring(0, 1000) + '...';
                document.getElementById('output').style.display = 'block';
                document.getElementById('downloadBtn').style.display = 'inline-block';
                
                console.log('JSON generated successfully!');
                
            } catch (error) {
                alert('Error generating JSON: ' + error.message);
                console.error('Error:', error);
            }
        }

        function downloadJSON() {
            if (!generatedJSON) {
                alert('Please generate JSON first!');
                return;
            }
            
            const blob = new Blob([generatedJSON], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'colors.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
