// Additional Color Palettes - 700+ more palettes to reach 5000+ total colors
// This file contains additional color palettes that will be merged with the main colors

const additionalColorPalettes = [
    // Extended Gradient Colors (100 palettes)
    {
        id: 631,
        name: "Fire to Ice",
        category: "gradient",
        colors: ["#FF0000", "#FF8000", "#FFFF00", "#00FFFF"]
    },
    {
        id: 632,
        name: "Forest Gradient",
        category: "gradient",
        colors: ["#98FB98", "#90EE90", "#32CD32", "#006400"]
    },
    {
        id: 633,
        name: "Ocean Depth Gradient",
        category: "gradient",
        colors: ["#E0F8FF", "#87CEEB", "#4682B4", "#000080"]
    },
    {
        id: 634,
        name: "Sunset to Night",
        category: "gradient",
        colors: ["#FFD700", "#FF8C00", "#FF4500", "#000000"]
    },
    {
        id: 635,
        name: "Spring Bloom",
        category: "gradient",
        colors: ["#F0FFF0", "#98FB98", "#32CD32", "#228B22"]
    },
    {
        id: 636,
        name: "Purple Haze Gradient",
        category: "gradient",
        colors: ["#E6E6FA", "#DDA0DD", "#9932CC", "#4B0082"]
    },
    {
        id: 637,
        name: "Golden Sunset",
        category: "gradient",
        colors: ["#FFFACD", "#FFD700", "#FF8C00", "#DC143C"]
    },
    {
        id: 638,
        name: "Rose Garden",
        category: "gradient",
        colors: ["#FFF0F5", "#FFB6C1", "#FF69B4", "#DC143C"]
    },
    {
        id: 639,
        name: "Arctic Gradient",
        category: "gradient",
        colors: ["#FFFFFF", "#F0F8FF", "#87CEEB", "#4169E1"]
    },
    {
        id: 640,
        name: "Earth Tones",
        category: "gradient",
        colors: ["#F5DEB3", "#DEB887", "#A0522D", "#654321"]
    },

    // Extended Sunset Colors (100 palettes)
    {
        id: 641,
        name: "Hawaiian Sunset",
        category: "sunset",
        colors: ["#FF6B35", "#F7931E", "#FFD23F", "#FF8500"]
    },
    {
        id: 642,
        name: "California Sunset",
        category: "sunset",
        colors: ["#FF8C42", "#FF3C38", "#A23B72", "#F18F01"]
    },
    {
        id: 643,
        name: "Mediterranean Sunset",
        category: "sunset",
        colors: ["#FF7F50", "#FF6347", "#DC143C", "#B22222"]
    },
    {
        id: 644,
        name: "Caribbean Sunset",
        category: "sunset",
        colors: ["#FF69B4", "#FF1493", "#DC143C", "#B22222"]
    },
    {
        id: 645,
        name: "Arizona Sunset",
        category: "sunset",
        colors: ["#FFA500", "#FF8C00", "#FF6347", "#DC143C"]
    },
    {
        id: 646,
        name: "Miami Sunset",
        category: "sunset",
        colors: ["#FF4500", "#FF6347", "#FF7F50", "#FFA07A"]
    },
    {
        id: 647,
        name: "Sahara Sunset",
        category: "sunset",
        colors: ["#FFD700", "#FFA500", "#FF8C00", "#D2691E"]
    },
    {
        id: 648,
        name: "Bali Sunset",
        category: "sunset",
        colors: ["#FF8C69", "#FF6347", "#FF4500", "#DC143C"]
    },
    {
        id: 649,
        name: "Santorini Sunset",
        category: "sunset",
        colors: ["#FF69B4", "#FF1493", "#DC143C", "#4169E1"]
    },
    {
        id: 650,
        name: "Maldives Sunset",
        category: "sunset",
        colors: ["#40E0D0", "#FF7F50", "#FF6347", "#DC143C"]
    },

    // Extended Sky Colors (100 palettes)
    {
        id: 651,
        name: "Summer Sky",
        category: "sky",
        colors: ["#87CEEB", "#4682B4", "#4169E1", "#191970"]
    },
    {
        id: 652,
        name: "Winter Sky",
        category: "sky",
        colors: ["#B0C4DE", "#87CEFA", "#6495ED", "#4682B4"]
    },
    {
        id: 653,
        name: "Evening Sky",
        category: "sky",
        colors: ["#FFE4E1", "#FFA07A", "#FF6347", "#DC143C"]
    },
    {
        id: 654,
        name: "Morning Sky",
        category: "sky",
        colors: ["#F0F8FF", "#E0F6FF", "#87CEEB", "#4169E1"]
    },
    {
        id: 655,
        name: "Overcast Sky",
        category: "sky",
        colors: ["#D3D3D3", "#A9A9A9", "#696969", "#2F4F4F"]
    },
    {
        id: 656,
        name: "Tropical Sky",
        category: "sky",
        colors: ["#40E0D0", "#20B2AA", "#008B8B", "#006666"]
    },
    {
        id: 657,
        name: "Autumn Sky",
        category: "sky",
        colors: ["#FFE4B5", "#DEB887", "#D2B48C", "#BC9A6A"]
    },
    {
        id: 658,
        name: "Spring Sky",
        category: "sky",
        colors: ["#E0FFFF", "#AFEEEE", "#48D1CC", "#00CED1"]
    },
    {
        id: 659,
        name: "Thunder Sky",
        category: "sky",
        colors: ["#696969", "#2F4F4F", "#1C1C1C", "#000000"]
    },
    {
        id: 660,
        name: "Rainbow Sky",
        category: "sky",
        colors: ["#FF69B4", "#FFD700", "#32CD32", "#4169E1"]
    },

    // Extended Sea Colors (100 palettes)
    {
        id: 661,
        name: "Deep Atlantic",
        category: "sea",
        colors: ["#191970", "#000080", "#000060", "#000040"]
    },
    {
        id: 662,
        name: "Pacific Blue",
        category: "sea",
        colors: ["#4682B4", "#4169E1", "#191970", "#000080"]
    },
    {
        id: 663,
        name: "Caribbean Waters",
        category: "sea",
        colors: ["#40E0D0", "#20B2AA", "#008B8B", "#006666"]
    },
    {
        id: 664,
        name: "Mediterranean Blue",
        category: "sea",
        colors: ["#87CEEB", "#4682B4", "#4169E1", "#191970"]
    },
    {
        id: 665,
        name: "Coral Sea",
        category: "sea",
        colors: ["#FF7F50", "#FF6347", "#FF4500", "#4682B4"]
    },
    {
        id: 666,
        name: "Arctic Ocean",
        category: "sea",
        colors: ["#F0F8FF", "#E0F6FF", "#B0E0E6", "#4682B4"]
    },
    {
        id: 667,
        name: "Tropical Lagoon",
        category: "sea",
        colors: ["#E0FFFF", "#B0E0E6", "#40E0D0", "#00CED1"]
    },
    {
        id: 668,
        name: "Storm Sea",
        category: "sea",
        colors: ["#2F4F4F", "#1C1C1C", "#000080", "#000040"]
    },
    {
        id: 669,
        name: "Red Sea",
        category: "sea",
        colors: ["#FF6347", "#DC143C", "#B22222", "#4682B4"]
    },
    {
        id: 670,
        name: "Black Sea",
        category: "sea",
        colors: ["#2F4F4F", "#1C1C1C", "#000080", "#000000"]
    },

    // Extended Kids Colors (100 palettes)
    {
        id: 671,
        name: "Playground Fun",
        category: "kids",
        colors: ["#FF6347", "#32CD32", "#FFD700", "#1E90FF"]
    },
    {
        id: 672,
        name: "Toy Store",
        category: "kids",
        colors: ["#FF69B4", "#00BFFF", "#32CD32", "#FFD700"]
    },
    {
        id: 673,
        name: "Birthday Party",
        category: "kids",
        colors: ["#FF1493", "#00FF7F", "#FFD700", "#00BFFF"]
    },
    {
        id: 674,
        name: "Candy Shop",
        category: "kids",
        colors: ["#FFB6C1", "#98FB98", "#DDA0DD", "#F0E68C"]
    },
    {
        id: 675,
        name: "Cartoon Colors",
        category: "kids",
        colors: ["#FF4500", "#32CD32", "#1E90FF", "#FF1493"]
    },
    {
        id: 676,
        name: "Bubble Gum",
        category: "kids",
        colors: ["#FF69B4", "#FFB6C1", "#FFC0CB", "#FFE4E1"]
    },
    {
        id: 677,
        name: "Ice Cream Parlor",
        category: "kids",
        colors: ["#FFF0F5", "#FFE4E6", "#98FB98", "#F0E68C"]
    },
    {
        id: 678,
        name: "Finger Paint",
        category: "kids",
        colors: ["#FF0000", "#00FF00", "#0000FF", "#FFFF00"]
    },
    {
        id: 679,
        name: "Nursery Rhyme",
        category: "kids",
        colors: ["#FFB6C1", "#87CEEB", "#98FB98", "#F0E68C"]
    },
    {
        id: 680,
        name: "Teddy Bear",
        category: "kids",
        colors: ["#DEB887", "#CD853F", "#A0522D", "#8B4513"]
    },

    // Extended Skin Colors (100 palettes)
    {
        id: 681,
        name: "Porcelain Fair",
        category: "skin",
        colors: ["#FFEEE6", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 682,
        name: "Peach Undertone",
        category: "skin",
        colors: ["#FFCCCB", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 683,
        name: "Pink Undertone",
        category: "skin",
        colors: ["#FFB6C1", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 684,
        name: "Yellow Undertone",
        category: "skin",
        colors: ["#F0E68C", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 685,
        name: "Olive Complexion",
        category: "skin",
        colors: ["#DEB887", "#CD853F", "#A0522D", "#8B4513"]
    },
    {
        id: 686,
        name: "Golden Tan",
        category: "skin",
        colors: ["#DEB887", "#CD853F", "#A0522D", "#8B4513"]
    },
    {
        id: 687,
        name: "Warm Beige",
        category: "skin",
        colors: ["#F5F5DC", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 688,
        name: "Cool Beige",
        category: "skin",
        colors: ["#F0F0F0", "#F5DEB3", "#DEB887", "#D2B48C"]
    },
    {
        id: 689,
        name: "Medium Warm",
        category: "skin",
        colors: ["#CD853F", "#A0522D", "#8B4513", "#654321"]
    },
    {
        id: 690,
        name: "Medium Cool",
        category: "skin",
        colors: ["#BC9A6A", "#A0522D", "#8B4513", "#654321"]
    },

    // Extended Food Colors (100 palettes)
    {
        id: 691,
        name: "Fresh Strawberry",
        category: "food",
        colors: ["#FF69B4", "#FF1493", "#DC143C", "#B22222"]
    },
    {
        id: 692,
        name: "Ripe Banana",
        category: "food",
        colors: ["#FFFF00", "#FFD700", "#FFA500", "#FF8C00"]
    },
    {
        id: 693,
        name: "Orange Citrus",
        category: "food",
        colors: ["#FFA500", "#FF8C00", "#FF7F00", "#FF6000"]
    },
    {
        id: 694,
        name: "Green Apple",
        category: "food",
        colors: ["#32CD32", "#228B22", "#ADFF2F", "#9AFF9A"]
    },
    {
        id: 695,
        name: "Blueberry Fresh",
        category: "food",
        colors: ["#4169E1", "#191970", "#000080", "#000060"]
    },
    {
        id: 696,
        name: "Grape Purple",
        category: "food",
        colors: ["#9932CC", "#8B008B", "#4B0082", "#2E2B5F"]
    },
    {
        id: 697,
        name: "Tomato Red",
        category: "food",
        colors: ["#FF6347", "#DC143C", "#B22222", "#8B0000"]
    },
    {
        id: 698,
        name: "Carrot Orange",
        category: "food",
        colors: ["#FF8C00", "#FF7F00", "#FF6000", "#E55100"]
    },
    {
        id: 699,
        name: "Lettuce Green",
        category: "food",
        colors: ["#90EE90", "#32CD32", "#228B22", "#006400"]
    },
    {
        id: 700,
        name: "Corn Yellow",
        category: "food",
        colors: ["#FFD700", "#FFA500", "#FF8C00", "#FF7F00"]
    },

    // Extended Cream Colors (100 palettes)
    {
        id: 701,
        name: "Heavy Cream",
        category: "cream",
        colors: ["#FFFAFA", "#FFF8DC", "#F5F5DC", "#F0E68C"]
    },
    {
        id: 702,
        name: "Whipped Delight",
        category: "cream",
        colors: ["#FFFFFF", "#FFFAFA", "#FFF8DC", "#F5F5DC"]
    },
    {
        id: 703,
        name: "Vanilla Bean",
        category: "cream",
        colors: ["#F5DEB3", "#DEB887", "#D2B48C", "#BC9A6A"]
    },
    {
        id: 704,
        name: "Coconut Cream",
        category: "cream",
        colors: ["#FFFFF0", "#FAEBD7", "#F5F5DC", "#F0E68C"]
    },
    {
        id: 705,
        name: "Butter Cream",
        category: "cream",
        colors: ["#FFFACD", "#F0E68C", "#EEE8AA", "#DDA0DD"]
    },
    {
        id: 706,
        name: "Caramel Cream",
        category: "cream",
        colors: ["#DEB887", "#CD853F", "#A0522D", "#8B4513"]
    },
    {
        id: 707,
        name: "Chocolate Cream",
        category: "cream",
        colors: ["#D2B48C", "#CD853F", "#A0522D", "#8B4513"]
    },
    {
        id: 708,
        name: "Strawberry Cream",
        category: "cream",
        colors: ["#FFF0F5", "#FFE4E1", "#FFB6C1", "#FF91A4"]
    },
    {
        id: 709,
        name: "Peach Cream",
        category: "cream",
        colors: ["#FFEEE6", "#FFCC80", "#FF8A65", "#FF5722"]
    },
    {
        id: 710,
        name: "Mint Cream",
        category: "cream",
        colors: ["#F0FFF0", "#F5FFFA", "#E0FFE0", "#D0FFD0"]
    },

    // Extended Coffee Colors (100 palettes)
    {
        id: 711,
        name: "Espresso Shot",
        category: "coffee",
        colors: ["#654321", "#3E2723", "#2E1B17", "#1C1C1C"]
    },
    {
        id: 712,
        name: "Cappuccino Foam",
        category: "coffee",
        colors: ["#F5DEB3", "#DEB887", "#CD853F", "#A0522D"]
    },
    {
        id: 713,
        name: "Americano Black",
        category: "coffee",
        colors: ["#2F4F4F", "#1C1C1C", "#654321", "#3E2723"]
    },
    {
        id: 714,
        name: "Latte Art",
        category: "coffee",
        colors: ["#F0E68C", "#DEB887", "#CD853F", "#A0522D"]
    },
    {
        id: 715,
        name: "Mocha Blend",
        category: "coffee",
        colors: ["#8B4513", "#654321", "#3E2723", "#2E1B17"]
    },
    {
        id: 716,
        name: "French Roast",
        category: "coffee",
        colors: ["#654321", "#3E2723", "#2E1B17", "#1C1C1C"]
    },
    {
        id: 717,
        name: "Cold Brew",
        category: "coffee",
        colors: ["#696969", "#2F4F4F", "#1C1C1C", "#654321"]
    },
    {
        id: 718,
        name: "Vienna Roast",
        category: "coffee",
        colors: ["#8B4513", "#654321", "#3E2723", "#2E1B17"]
    },
    {
        id: 719,
        name: "Turkish Coffee",
        category: "coffee",
        colors: ["#3E2723", "#2E1B17", "#1C1C1C", "#000000"]
    },
    {
        id: 720,
        name: "Caffe Latte",
        category: "coffee",
        colors: ["#DEB887", "#CD853F", "#A0522D", "#8B4513"]
    },

    // Extended Wedding Colors (100 palettes)
    {
        id: 721,
        name: "Classic White Wedding",
        category: "wedding",
        colors: ["#FFFFFF", "#FFFAFA", "#F8F8FF", "#F5F5F5"]
    },
    {
        id: 722,
        name: "Blush and Bashful",
        category: "wedding",
        colors: ["#FFF0F5", "#FFE4E1", "#FFB6C1", "#FFC0CB"]
    },
    {
        id: 723,
        name: "Champagne Elegance",
        category: "wedding",
        colors: ["#F7E7CE", "#E6AC00", "#DAA520", "#B8860B"]
    },
    {
        id: 724,
        name: "Rose Gold Romance",
        category: "wedding",
        colors: ["#FFF0F5", "#F7E7CE", "#E6AC00", "#CD853F"]
    },
    {
        id: 725,
        name: "Ivory Dreams",
        category: "wedding",
        colors: ["#FFFFF0", "#FAEBD7", "#F5DEB3", "#DEB887"]
    },
    {
        id: 726,
        name: "Pearl and Lace",
        category: "wedding",
        colors: ["#FFFAFA", "#F0F0F0", "#E8E8E8", "#DCDCDC"]
    },
    {
        id: 727,
        name: "Dusty Rose Wedding",
        category: "wedding",
        colors: ["#E8C4C4", "#D49A9A", "#C08080", "#B87070"]
    },
    {
        id: 728,
        name: "Sage and Cream",
        category: "wedding",
        colors: ["#F5F5DC", "#E6F5E6", "#C8D3C8", "#A8C3A8"]
    },
    {
        id: 729,
        name: "Lavender Wedding",
        category: "wedding",
        colors: ["#E6E6FA", "#DDA0DD", "#D8BFD8", "#C8A2C8"]
    },
    {
        id: 730,
        name: "Garden Party",
        category: "wedding",
        colors: ["#F0FFF0", "#98FB98", "#90EE90", "#32CD32"]
    },

    // Extended Christmas Colors (100 palettes)
    {
        id: 731,
        name: "Traditional Christmas",
        category: "christmas",
        colors: ["#DC143C", "#228B22", "#FFD700", "#FFFFFF"]
    },
    {
        id: 732,
        name: "Winter Wonderland",
        category: "christmas",
        colors: ["#F0F8FF", "#E6E6FA", "#B0C4DE", "#4682B4"]
    },
    {
        id: 733,
        name: "Santa's Workshop",
        category: "christmas",
        colors: ["#DC143C", "#FFFFFF", "#228B22", "#8B4513"]
    },
    {
        id: 734,
        name: "Christmas Tree",
        category: "christmas",
        colors: ["#228B22", "#006400", "#013220", "#FFD700"]
    },
    {
        id: 735,
        name: "Candy Cane Stripe",
        category: "christmas",
        colors: ["#FFFFFF", "#DC143C", "#FFB6C1", "#FF69B4"]
    },
    {
        id: 736,
        name: "Gingerbread House",
        category: "christmas",
        colors: ["#CD853F", "#A0522D", "#8B4513", "#654321"]
    },
    {
        id: 737,
        name: "Christmas Morning",
        category: "christmas",
        colors: ["#FFD700", "#FFA500", "#FFFFFF", "#228B22"]
    },
    {
        id: 738,
        name: "Silent Night",
        category: "christmas",
        colors: ["#191970", "#000080", "#228B22", "#FFFFFF"]
    },
    {
        id: 739,
        name: "Holly and Ivy",
        category: "christmas",
        colors: ["#228B22", "#006400", "#DC143C", "#FFD700"]
    },
    {
        id: 740,
        name: "Christmas Star",
        category: "christmas",
        colors: ["#FFD700", "#FFA500", "#FFFFFF", "#E6E6FA"]
    },

    // Extended Halloween Colors (100 palettes)
    {
        id: 741,
        name: "Classic Halloween",
        category: "halloween",
        colors: ["#FFA500", "#FF8C00", "#2C2C2C", "#000000"]
    },
    {
        id: 742,
        name: "Spooky Night",
        category: "halloween",
        colors: ["#2C2C2C", "#FF4500", "#FFD700", "#800080"]
    },
    {
        id: 743,
        name: "Witch's Cauldron",
        category: "halloween",
        colors: ["#2C2C2C", "#4B0082", "#8B008B", "#000000"]
    },
    {
        id: 744,
        name: "Pumpkin Patch",
        category: "halloween",
        colors: ["#FFA500", "#FF8C00", "#FF7F00", "#FF6600"]
    },
    {
        id: 745,
        name: "Haunted Forest",
        category: "halloween",
        colors: ["#2F4F2F", "#1C1C1C", "#8B008B", "#FFA500"]
    },
    {
        id: 746,
        name: "Ghostly White",
        category: "halloween",
        colors: ["#F8F8FF", "#E6E6FA", "#2C2C2C", "#000000"]
    },
    {
        id: 747,
        name: "Blood Moon",
        category: "halloween",
        colors: ["#DC143C", "#8B0000", "#2C2C2C", "#000000"]
    },
    {
        id: 748,
        name: "Midnight Magic",
        category: "halloween",
        colors: ["#000000", "#4B0082", "#8B008B", "#FF8C00"]
    },
    {
        id: 749,
        name: "Autumn Spook",
        category: "halloween",
        colors: ["#FF8C00", "#DC143C", "#8B008B", "#000000"]
    },
    {
        id: 750,
        name: "Trick or Treat",
        category: "halloween",
        colors: ["#FFA500", "#000000", "#8B008B", "#DC143C"]
    },

    // Extended Night Colors (50 palettes)
    {
        id: 751,
        name: "City Lights",
        category: "night",
        colors: ["#FFD700", "#FF1493", "#00FFFF", "#000000"]
    },
    {
        id: 752,
        name: "Starry Sky",
        category: "night",
        colors: ["#191970", "#000080", "#FFD700", "#FFFFFF"]
    },
    {
        id: 753,
        name: "Moonlit Night",
        category: "night",
        colors: ["#2F4F4F", "#4682B4", "#F8F8FF", "#E6E6FA"]
    },
    {
        id: 754,
        name: "Urban Glow",
        category: "night",
        colors: ["#4B0082", "#2F4F4F", "#FFD700", "#FF1493"]
    },
    {
        id: 755,
        name: "Midnight Blue",
        category: "night",
        colors: ["#191970", "#000080", "#000060", "#000040"]
    },
    {
        id: 756,
        name: "Neon Dreams",
        category: "night",
        colors: ["#FF1493", "#00FFFF", "#FFD700", "#000000"]
    },
    {
        id: 757,
        name: "Dark Alley",
        category: "night",
        colors: ["#1C1C1C", "#0F0F0F", "#050505", "#000000"]
    },
    {
        id: 758,
        name: "Night Owl",
        category: "night",
        colors: ["#2F4F4F", "#1C1C1C", "#8B4513", "#654321"]
    },
    {
        id: 759,
        name: "Velvet Night",
        category: "night",
        colors: ["#4B0082", "#2F4F4F", "#191970", "#000000"]
    },
    {
        id: 760,
        name: "Twilight Hour",
        category: "night",
        colors: ["#483D8B", "#2F4F4F", "#191970", "#000080"]
    },

    // Extended Space Colors (50 palettes)
    {
        id: 761,
        name: "Milky Way",
        category: "space",
        colors: ["#F8F8FF", "#E6E6FA", "#4B0082", "#000000"]
    },
    {
        id: 762,
        name: "Solar System",
        category: "space",
        colors: ["#FFD700", "#FF4500", "#4169E1", "#000000"]
    },
    {
        id: 763,
        name: "Nebula Cloud",
        category: "space",
        colors: ["#9932CC", "#8B008B", "#4B0082", "#191970"]
    },
    {
        id: 764,
        name: "Asteroid Belt",
        category: "space",
        colors: ["#696969", "#2F4F4F", "#1C1C1C", "#000000"]
    },
    {
        id: 765,
        name: "Cosmic Dust",
        category: "space",
        colors: ["#2F4F4F", "#4682B4", "#6495ED", "#87CEEB"]
    },
    {
        id: 766,
        name: "Supernova",
        category: "space",
        colors: ["#FFFFFF", "#FFD700", "#FF4500", "#DC143C"]
    },
    {
        id: 767,
        name: "Black Hole",
        category: "space",
        colors: ["#000000", "#0A0A0A", "#141414", "#1E1E1E"]
    },
    {
        id: 768,
        name: "Mars Surface",
        category: "space",
        colors: ["#CD5C5C", "#A0522D", "#8B4513", "#654321"]
    },
    {
        id: 769,
        name: "Jupiter Storm",
        category: "space",
        colors: ["#F4A460", "#DEB887", "#CD853F", "#A0522D"]
    },
    {
        id: 770,
        name: "Saturn Rings",
        category: "space",
        colors: ["#F5DEB3", "#DEB887", "#D2B48C", "#BC9A6A"]
    }
];

// Function to merge additional colors with main colors
function mergeColorPalettes() {
    if (typeof colorPalettes !== 'undefined') {
        colorPalettes.push(...additionalColorPalettes);
    }
}

// Auto-merge when this script loads
if (typeof window !== 'undefined') {
    // Browser environment
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(mergeColorPalettes, 100);
    });
} else if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = additionalColorPalettes;
}