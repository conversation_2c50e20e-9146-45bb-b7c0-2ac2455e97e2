// Automatic Color Generator - Generates additional colors programmatically
// This ensures we reach 5000+ total colors by generating variations

function generateColorVariations() {
    const generatedPalettes = [];
    let currentId = 1000; // Start from ID 1000 to avoid conflicts

    // Base colors for each category
    const baseColors = {
        pastel: ['#FFB6C1', '#E6E6FA', '#98FB98', '#F0E68C'],
        vintage: ['#DEB887', '#CD853F', '#BC8F8F', '#D2B48C'],
        retro: ['#FF69B4', '#00FFFF', '#FFD700', '#9370DB'],
        neon: ['#39FF14', '#FF073A', '#00FFFF', '#FF1493'],
        gold: ['#FFD700', '#FFA500', '#FF8C00', '#DAA520'],
        light: ['#FFFFFF', '#F8F8FF', '#F0F0F0', '#E8E8E8'],
        dark: ['#000000', '#1C1C1C', '#2F4F4F', '#696969'],
        warm: ['#FF4500', '#FF8C00', '#CD853F', '#D2691E'],
        cold: ['#87CEEB', '#4682B4', '#B0E0E6', '#E0FFFF'],
        nature: ['#228B22', '#32CD32', '#8FBC8F', '#9ACD32'],
        earth: ['#8B4513', '#A0522D', '#CD853F', '#DEB887'],
        summer: ['#FF6347', '#40E0D0', '#FFD700', '#32CD32'],
        fall: ['#FF8C00', '#D2691E', '#A0522D', '#8B4513'],
        winter: ['#FFFFFF', '#B0C4DE', '#2F4F4F', '#87CEEB'],
        spring: ['#FFB6C1', '#90EE90', '#DDA0DD', '#E0FFFF'],
        happy: ['#FFD700', '#FF69B4', '#32CD32', '#00BFFF'],
        night: ['#191970', '#000080', '#2F4F4F', '#1C1C1C'],
        space: ['#4B0082', '#191970', '#9932CC', '#8B008B'],
        rainbow: ['#FF0000', '#FFFF00', '#00FF00', '#0000FF'],
        gradient: ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5'],
        sunset: ['#FF8C42', '#FF3C38', '#A23B72', '#F18F01'],
        sky: ['#87CEEB', '#4682B4', '#4169E1', '#191970'],
        sea: ['#006994', '#4682B4', '#4169E1', '#191970'],
        kids: ['#FF69B4', '#00BFFF', '#32CD32', '#FFD700'],
        skin: ['#F5DEB3', '#DEB887', '#CD853F', '#A0522D'],
        food: ['#FF6347', '#32CD32', '#FFD700', '#9932CC'],
        cream: ['#FFFAFA', '#FFF8DC', '#F0E68C', '#EEE8AA'],
        coffee: ['#8B4513', '#A0522D', '#CD853F', '#DEB887'],
        wedding: ['#FFFFFF', '#FFB6C1', '#E6E6FA', '#F0E68C'],
        christmas: ['#DC143C', '#228B22', '#FFD700', '#FFFFFF'],
        halloween: ['#FF8C00', '#000000', '#8B008B', '#DC143C']
    };

    // Color variation functions
    function lightenColor(color, amount) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * amount);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000
            + (G < 255 ? G < 1 ? 0 : G : 255) * 0x100
            + (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    function darkenColor(color, amount) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * amount);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000
            + (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100
            + (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    function adjustSaturation(color, amount) {
        const num = parseInt(color.replace("#", ""), 16);
        const R = (num >> 16);
        const G = (num >> 8 & 0x00FF);
        const B = (num & 0x0000FF);
        
        const max = Math.max(R, G, B);
        const min = Math.min(R, G, B);
        const diff = max - min;
        
        if (diff === 0) return color;
        
        const newR = Math.round(R + (R - (R + G + B) / 3) * amount);
        const newG = Math.round(G + (G - (R + G + B) / 3) * amount);
        const newB = Math.round(B + (B - (R + G + B) / 3) * amount);
        
        return "#" + (0x1000000 + 
            (newR > 255 ? 255 : newR < 0 ? 0 : newR) * 0x10000 +
            (newG > 255 ? 255 : newG < 0 ? 0 : newG) * 0x100 +
            (newB > 255 ? 255 : newB < 0 ? 0 : newB)).toString(16).slice(1);
    }

    // Generate variations for each category
    Object.keys(baseColors).forEach(category => {
        const base = baseColors[category];
        
        // Generate 50 variations per category (200 additional colors per category)
        for (let i = 0; i < 50; i++) {
            const variations = [];
            
            // Create 4 related colors for each palette
            for (let j = 0; j < 4; j++) {
                let color = base[j % base.length];
                
                // Apply various transformations
                if (i < 10) {
                    color = lightenColor(color, 10 + i * 5);
                } else if (i < 20) {
                    color = darkenColor(color, 10 + (i - 10) * 5);
                } else if (i < 30) {
                    color = adjustSaturation(color, 0.3 + (i - 20) * 0.1);
                } else if (i < 40) {
                    color = lightenColor(darkenColor(color, 20), 30);
                } else {
                    // Blend with neighboring colors
                    const nextColor = base[(j + 1) % base.length];
                    const blend = blendColors(color, nextColor, 0.5);
                    color = blend;
                }
                
                variations.push(color);
            }
            
            generatedPalettes.push({
                id: currentId++,
                name: `${category.charAt(0).toUpperCase() + category.slice(1)} Variation ${i + 1}`,
                category: category,
                colors: variations
            });
        }
    });

    return generatedPalettes;
}

function blendColors(color1, color2, ratio) {
    const hex = (c) => parseInt(c.replace("#", ""), 16);
    const c1 = hex(color1);
    const c2 = hex(color2);
    
    const r1 = (c1 >> 16) & 255;
    const g1 = (c1 >> 8) & 255;
    const b1 = c1 & 255;
    
    const r2 = (c2 >> 16) & 255;
    const g2 = (c2 >> 8) & 255;
    const b2 = c2 & 255;
    
    const r = Math.round(r1 * (1 - ratio) + r2 * ratio);
    const g = Math.round(g1 * (1 - ratio) + g2 * ratio);
    const b = Math.round(b1 * (1 - ratio) + b2 * ratio);
    
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

// Generate gradient palettes
function generateGradientPalettes() {
    const gradients = [];
    let id = 2000;
    
    const gradientTypes = [
        { name: "Fire", colors: ["#FF0000", "#FF4500", "#FF8C00", "#FFD700"] },
        { name: "Ocean", colors: ["#000080", "#4169E1", "#87CEEB", "#E0F8FF"] },
        { name: "Forest", colors: ["#013220", "#228B22", "#32CD32", "#98FB98"] },
        { name: "Sunset", colors: ["#FFD700", "#FF8C00", "#FF4500", "#DC143C"] },
        { name: "Purple", colors: ["#4B0082", "#8B008B", "#9932CC", "#DDA0DD"] },
        { name: "Pink", colors: ["#8B008B", "#FF1493", "#FF69B4", "#FFB6C1"] },
        { name: "Cyan", colors: ["#008B8B", "#00CED1", "#40E0D0", "#E0FFFF"] },
        { name: "Brown", colors: ["#654321", "#8B4513", "#A0522D", "#DEB887"] }
    ];
    
    gradientTypes.forEach(type => {
        for (let i = 0; i < 25; i++) {
            gradients.push({
                id: id++,
                name: `${type.name} Gradient ${i + 1}`,
                category: "gradient",
                colors: type.colors.map(color => 
                    i < 10 ? lightenColor(color, i * 5) :
                    i < 20 ? darkenColor(color, (i - 10) * 5) :
                    adjustSaturation(color, 0.2 + (i - 20) * 0.15)
                )
            });
        }
    });
    
    return gradients;
}

// Function to add all generated colors
function addGeneratedColors() {
    if (typeof colorPalettes !== 'undefined') {
        const variations = generateColorVariations();
        const gradients = generateGradientPalettes();
        
        colorPalettes.push(...variations);
        colorPalettes.push(...gradients);
        
        console.log(`Generated ${variations.length} color variations`);
        console.log(`Generated ${gradients.length} gradient palettes`);
        console.log(`Total palettes after generation: ${colorPalettes.length}`);
        console.log(`Total individual colors: ${colorPalettes.length * 4}`);
        
        // Update the UI if the render function exists
        if (typeof renderColorPalettes === 'function') {
            setTimeout(() => {
                renderColorPalettes();
                updateResultsCount();
            }, 100);
        }
    }
}

// Auto-generate colors when this script loads
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(addGeneratedColors, 500);
    });
}