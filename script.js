// Global variables
let filteredPalettes = [];
let currentFilter = 'all';

// Initialize filtered palettes after all colors are loaded
function initializeFilteredPalettes() {
    if (typeof colorPalettes !== 'undefined' && colorPalettes.length > 0) {
        filteredPalettes = [...colorPalettes];
        renderColorPalettes();
        updateResultsCount();
        console.log(`Initialized with ${colorPalettes.length} palettes (${colorPalettes.length * 4} individual colors)`);
    } else {
        // Retry after a short delay if colors aren't loaded yet
        setTimeout(initializeFilteredPalettes, 100);
    }
}

// DOM elements
const colorGrid = document.getElementById('colorGrid');
const searchInput = document.getElementById('searchInput');
const resultsCount = document.getElementById('resultsCount');
const filterButtons = document.querySelectorAll('.filter-btn');
const clearFilterBtn = document.getElementById('clearFilter');
const toast = document.getElementById('toast');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Wait for all color files to load before initializing
    setTimeout(() => {
        initializeFilteredPalettes();
        setupEventListeners();
    }, 1000); // Give time for all color generators to complete
});

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    
    // Filter buttons
    filterButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const category = e.target.dataset.category;
            setActiveFilter(category);
            filterPalettes(category);
        });
    });
    
    // Clear filter button
    clearFilterBtn.addEventListener('click', () => {
        setActiveFilter('all');
        filterPalettes('all');
        searchInput.value = '';
    });
    
    // Mobile nav toggle (if needed)
    setupMobileNav();
}

// Mobile navigation setup
function setupMobileNav() {
    // Add mobile menu toggle if screen is small
    if (window.innerWidth <= 1024) {
        addMobileMenuToggle();
    }
    
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 1024) {
            addMobileMenuToggle();
        }
    });
}

function addMobileMenuToggle() {
    let mobileToggle = document.querySelector('.mobile-toggle');
    if (!mobileToggle) {
        mobileToggle = document.createElement('button');
        mobileToggle.className = 'mobile-toggle';
        mobileToggle.innerHTML = '☰';
        mobileToggle.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 200;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 18px;
            cursor: pointer;
        `;
        document.body.appendChild(mobileToggle);
        
        mobileToggle.addEventListener('click', () => {
            const sidebar = document.querySelector('.nav-sidebar');
            sidebar.classList.toggle('open');
        });
    }
}

// Render color palettes
function renderColorPalettes() {
    colorGrid.innerHTML = '';
    
    filteredPalettes.forEach(palette => {
        const card = createColorCard(palette);
        colorGrid.appendChild(card);
    });
}

// Create individual color card
function createColorCard(palette) {
    const card = document.createElement('div');
    card.className = 'color-card';
    card.style.setProperty('--color1', palette.colors[0]);
    card.style.setProperty('--color2', palette.colors[1]);
    card.style.setProperty('--color3', palette.colors[2]);
    card.style.setProperty('--color4', palette.colors[3]);
    
    card.innerHTML = `
        <div class="card-header">
            <h3 class="card-title">${palette.name}</h3>
            <span class="card-category">${palette.category}</span>
        </div>
        <div class="color-palette">
            ${palette.colors.map((color, index) => `
                <div class="color-item" data-color="${color}">
                    <div class="color-swatch" style="background-color: ${color}">
                        <div class="color-code">
                            ${color}
                            <button class="copy-btn" onclick="copyToClipboard('${color}')">Copy</button>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
    
    return card;
}

// Filter palettes by category
function filterPalettes(category) {
    currentFilter = category;
    
    if (category === 'all') {
        filteredPalettes = [...colorPalettes];
    } else {
        filteredPalettes = colorPalettes.filter(palette => 
            palette.category.toLowerCase() === category.toLowerCase()
        );
    }
    
    // Apply search filter if there's a search term
    const searchTerm = searchInput.value.trim();
    if (searchTerm) {
        applySearchFilter(searchTerm);
    }
    
    renderColorPalettes();
    updateResultsCount();
}

// Handle search functionality
function handleSearch(e) {
    const searchTerm = e.target.value.trim().toLowerCase();
    applySearchFilter(searchTerm);
}

function applySearchFilter(searchTerm) {
    if (!searchTerm) {
        filterPalettes(currentFilter);
        return;
    }
    
    // Start with category filtered palettes
    let basePalettes = currentFilter === 'all' ? colorPalettes : 
        colorPalettes.filter(palette => palette.category.toLowerCase() === currentFilter.toLowerCase());
    
    // Apply search filter
    filteredPalettes = basePalettes.filter(palette => {
        // Search in name, category, and color codes
        const nameMatch = palette.name.toLowerCase().includes(searchTerm);
        const categoryMatch = palette.category.toLowerCase().includes(searchTerm);
        const colorMatch = palette.colors.some(color => 
            color.toLowerCase().includes(searchTerm)
        );
        
        return nameMatch || categoryMatch || colorMatch;
    });
    
    renderColorPalettes();
    updateResultsCount();
}

// Set active filter button
function setActiveFilter(category) {
    filterButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.category === category) {
            btn.classList.add('active');
        }
    });
}

// Update results count
function updateResultsCount() {
    const count = filteredPalettes.length;
    const total = colorPalettes ? colorPalettes.length : 0;
    const totalColors = total * 4;
    const filteredColors = count * 4;
    
    if (currentFilter === 'all' && !searchInput.value.trim()) {
        resultsCount.textContent = `Showing all ${total.toLocaleString()} color palettes (${totalColors.toLocaleString()} individual colors)`;
    } else {
        resultsCount.textContent = `Showing ${count.toLocaleString()} of ${total.toLocaleString()} palettes (${filteredColors.toLocaleString()} of ${totalColors.toLocaleString()} colors)`;
    }
}

// Copy color code to clipboard
async function copyToClipboard(colorCode) {
    try {
        await navigator.clipboard.writeText(colorCode);
        showToast(`${colorCode} copied to clipboard!`);
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = colorCode;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(`${colorCode} copied to clipboard!`);
    }
}

// Show toast notification
function showToast(message) {
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Focus search on Ctrl/Cmd + F
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        searchInput.focus();
    }
    
    // Clear filters on Escape
    if (e.key === 'Escape') {
        if (searchInput === document.activeElement) {
            searchInput.blur();
        } else {
            setActiveFilter('all');
            filterPalettes('all');
            searchInput.value = '';
        }
    }
});

// Smooth scrolling for better UX
function smoothScrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Add scroll to top functionality when filter changes
function handleFilterChange() {
    setTimeout(() => {
        if (window.scrollY > 200) {
            smoothScrollToTop();
        }
    }, 100);
}

// Update filter functions to include scroll
const originalFilterPalettes = filterPalettes;
filterPalettes = function(category) {
    originalFilterPalettes(category);
    handleFilterChange();
};

// Performance optimization: Debounce search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to search
const debouncedSearch = debounce(handleSearch, 300);
searchInput.removeEventListener('input', handleSearch);
searchInput.addEventListener('input', debouncedSearch);

// Add loading animation for better UX
function showLoading() {
    colorGrid.innerHTML = '<div class="loading">Loading color palettes...</div>';
}

function hideLoading() {
    const loading = document.querySelector('.loading');
    if (loading) {
        loading.remove();
    }
}

// Intersection Observer for lazy loading (if needed for more palettes)
function setupLazyLoading() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Load more palettes if needed
                console.log('Load more palettes');
            }
        });
    });
    
    // Observe the last card for infinite scroll (if implementing)
    const cards = document.querySelectorAll('.color-card');
    if (cards.length > 0) {
        observer.observe(cards[cards.length - 1]);
    }
}

// Analytics or tracking (placeholder)
function trackColorCopy(colorCode) {
    // Add analytics tracking here if needed
    console.log(`Color copied: ${colorCode}`);
}

// Update copy function to include tracking
const originalCopyToClipboard = copyToClipboard;
copyToClipboard = async function(colorCode) {
    await originalCopyToClipboard(colorCode);
    trackColorCopy(colorCode);
};

// Export functions for potential future use
window.ColorPaletteHub = {
    filterPalettes,
    copyToClipboard,
    showToast,
    renderColorPalettes
};
